<template>
  <view class="profile-container">
    <!-- 用户信息头部 - 重新设计 -->
    <view class="user-header">
      <view class="user-content">
        <view class="avatar-section">
          <view class="avatar-wrapper">
            <image
              class="avatar"
              :src="userInfo.avatar || '/static/default-avatar.svg'"
              mode="aspectFill"
            />
            <view v-if="userInfo.isLogin" class="status-indicator online"></view>
            <view v-else class="status-indicator offline"></view>
          </view>
          <view class="user-info">
            <text class="username">{{ userInfo.nickname || '未登录用户' }}</text>
            <text class="user-id">{{ getUserIdText() }}</text>
            <view v-if="userInfo.isLogin" class="user-badge">
              <text class="badge-text">已认证</text>
            </view>
          </view>
        </view>
        <button
          v-if="!userInfo.isLogin"
          @click="handleWechatLogin"
          class="btn btn-primary login-btn"
        >
          <text class="btn-icon">👤</text>
          <text>微信登录</text>
        </button>
      </view>
    </view>

    <!-- 统计信息卡片 - 响应式重新设计 -->
    <view class="stats-section">
      <view class="section-header">
        <text class="section-title">使用统计</text>
        <text class="section-subtitle">您的使用数据概览</text>
      </view>

      <view class="stats-container">
        <!-- 主要统计 -->
        <view class="primary-stats">
          <view class="primary-stat-item">
            <view class="stat-icon-wrapper primary">
              <text class="stat-icon">📊</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.totalTasks }}</text>
              <text class="stat-label">处理任务</text>
            </view>
          </view>
          <view class="primary-stat-item">
            <view class="stat-icon-wrapper success">
              <text class="stat-icon">✅</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.completedTasks }}</text>
              <text class="stat-label">已完成</text>
            </view>
          </view>
        </view>

        <!-- 次要统计 -->
        <view class="secondary-stats">
          <view class="secondary-stat-item">
            <view class="stat-icon-wrapper info">
              <text class="stat-icon">⏱️</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.totalDuration }}</text>
              <text class="stat-label">总时长(分钟)</text>
            </view>
          </view>
          <view class="secondary-stat-item">
            <view class="stat-icon-wrapper warning">
              <text class="stat-icon">📁</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ formatFileSize(stats.totalSize) }}</text>
              <text class="stat-label">处理文件</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 - 重新设计 -->
    <view class="menu-section">
      <view class="section-header">
        <text class="section-title">功能菜单</text>
        <text class="section-subtitle">快速访问常用功能</text>
      </view>

      <view class="menu-container">
        <view class="menu-item" @click="navigateTo('/pages/history/history')">
          <view class="menu-icon-wrapper history">
            <text class="menu-icon">📋</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">历史记录</text>
            <text class="menu-desc">查看处理历史</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>

        <view class="menu-item" @click="showSettings">
          <view class="menu-icon-wrapper settings">
            <text class="menu-icon">⚙️</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">设置</text>
            <text class="menu-desc">个性化设置</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>

        <view class="menu-item" @click="showAbout">
          <view class="menu-icon-wrapper about">
            <text class="menu-icon">ℹ️</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">关于我们</text>
            <text class="menu-desc">了解更多信息</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>

        <view class="menu-item" @click="showHelp">
          <view class="menu-icon-wrapper help">
            <text class="menu-icon">❓</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">帮助与反馈</text>
            <text class="menu-desc">获取帮助支持</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 - 重新设计 -->
    <view class="footer-section">
      <view class="version-info">
        <text class="version-text">智能字幕胶囊 v1.0.0</text>
        <text class="copyright-text">© 2024 All Rights Reserved</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId } from '@/utils/common'
import { getWechatOpenid, updateUserInfo } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  openid: '',
  isLogin: false
})

// 统计信息
const stats = ref({
  totalTasks: 0,
  completedTasks: 0,
  totalDuration: 0,
  totalSize: 0
})

// 页面加载时获取用户信息和统计数据
onMounted(async () => {
  await loadUserInfo()
  await loadStats()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId

    // 尝试从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo) {
      userInfo.value.nickname = savedUserInfo.nickname
      userInfo.value.avatar = savedUserInfo.avatar
      userInfo.value.openid = savedUserInfo.openid || ''
      userInfo.value.isLogin = true
    } else {
      userInfo.value.nickname = `用户${deviceId.slice(-6)}`
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取用户ID显示文本
const getUserIdText = (): string => {
  if (userInfo.value.isLogin) {
    return `ID: ${userInfo.value.deviceId?.slice(-8) || 'N/A'}`
  }
  return '点击登录获取更多功能'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 由于移除了视频处理功能，这里只显示登录相关的统计
    stats.value.totalTasks = 0
    stats.value.completedTasks = 0
    stats.value.totalDuration = 0
    stats.value.totalSize = 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 微信登录
const handleWechatLogin = async () => {
  try {
    uni.showLoading({ title: '登录中...' })

    // 第一步：先获取用户信息授权（必须在用户点击事件中立即调用）
    const userProfile = await new Promise<any>((resolve, reject) => {
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      })
    })

    // 第二步：调用 uni.login 获取 code
    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    if (!loginRes.code) {
      throw new Error('获取登录凭证失败')
    }

    console.log('获取到微信登录code:', loginRes.code)

    // 第三步：调用服务端接口获取 openid
    uni.showLoading({ title: '获取用户信息中...' })

    const openidResult = await getWechatOpenid(loginRes.code)
    console.log('获取openid结果:', openidResult)

    // 验证返回结果
    if (openidResult.errCode !== 0) {
      throw new Error(openidResult.errMsg || '获取openid失败')
    }

    if (!openidResult.data || !openidResult.data.openid) {
      throw new Error('获取用户openid失败')
    }

    const { openid, isNewUser } = openidResult.data

    // 第四步：更新服务端用户信息
    if (userProfile.userInfo.nickName || userProfile.userInfo.avatarUrl) {
      try {
        await updateUserInfo(
          openid,
          userProfile.userInfo.nickName,
          userProfile.userInfo.avatarUrl
        )
        console.log('用户信息更新成功')
      } catch (updateError) {
        console.warn('更新用户信息失败，但不影响登录:', updateError)
      }
    }

    // 第五步：更新本地用户信息
    userInfo.value.nickname = userProfile.userInfo.nickName
    userInfo.value.avatar = userProfile.userInfo.avatarUrl
    userInfo.value.openid = openid
    userInfo.value.isLogin = true

    // 保存用户信息到本地存储
    const userInfoToSave = {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      openid: userInfo.value.openid,
      loginTime: Date.now(),
      isNewUser: isNewUser
    }

    uni.setStorageSync('userInfo', userInfoToSave)

    uni.hideLoading()

    // 显示登录成功提示
    const successMessage = isNewUser ? '注册成功' : '登录成功'
    uni.showToast({
      title: successMessage,
      icon: 'success'
    })

    console.log('微信登录完成:', {
      openid: openid,
      nickname: userInfo.value.nickname,
      isNewUser: isNewUser
    })

  } catch (error: any) {
    uni.hideLoading()
    console.error('微信登录失败:', error)

    let errorMessage = '登录失败'

    // 处理不同类型的错误
    if (error.errMsg) {
      if (error.errMsg.includes('auth deny')) {
        errorMessage = '用户拒绝授权'
      } else if (error.errMsg.includes('auth cancel')) {
        errorMessage = '用户取消登录'
      } else if (error.errMsg.includes('can only be invoked by user TAP gesture')) {
        errorMessage = '请直接点击登录按钮进行授权'
      }
    } else if (error.message) {
      if (error.message.includes('获取登录凭证失败')) {
        errorMessage = '获取登录凭证失败，请重试'
      } else if (error.message.includes('获取用户openid失败')) {
        errorMessage = '获取用户信息失败，请重试'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络'
      }
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    })
  }
}

// 页面导航
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '智能字幕胶囊是一款AI驱动的视频字幕生成工具，支持视频添加字幕、字幕翻译、视频去水印等功能。',
    showCancel: false
  })
}

// 显示设置
const showSettings = () => {
  uni.showModal({
    title: '设置',
    content: '设置功能正在开发中，敬请期待！',
    showCancel: false
  })
}

// 显示帮助信息
const showHelp = () => {
  uni.showModal({
    title: '帮助与反馈',
    content: '如有问题或建议，请联系我们的客服团队。我们将竭诚为您服务！',
    showCancel: false
  })
}
</script>

<style scoped>

.profile-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding-bottom: 192rpx; /* 为tabbar留出空间 */
}

/* ==================== 用户头部区域 ==================== */
.user-header {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  padding: 160rpx 32rpx 96rpx;
  position: relative;
  overflow: hidden;
}

.user-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.user-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 32rpx;
}

.avatar-section {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 32rpx;
}

.avatar-wrapper {
  position: relative;
  flex-shrink: 0;
}

.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 9999rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);
  transition: all 0.2s ease-out;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 32rpx 128rpx rgba(0, 0, 0, 0.24);
}

.status-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 9999rpx;
  border: 4rpx solid white;
}

.status-indicator.online {
  background-color: #22c55e;
  box-shadow: 0 0 0 4rpx rgba(34, 197, 94, 0.2);
}

.status-indicator.offline {
  background-color: #a3a3a3;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8rpx;
}

.username {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  line-height: 1.25;
}

.user-id {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.user-badge {
  margin-top: 8rpx;
}

.badge-text {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 9999rpx;
  backdrop-filter: blur(10rpx);
}

.login-btn {
  flex-shrink: 0;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.login-btn .btn-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
}

/* ==================== 统计信息区域 ==================== */
.stats-section {
  padding: 0 32rpx;
  margin-bottom: 64rpx;
}

.section-header {
  margin-bottom: 48rpx;
  text-align: center;
}

.section-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #171717;
  margin-bottom: 8rpx;
}

.section-subtitle {
  display: block;
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 主要统计 - 更突出的展示 */
.primary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.primary-stat-item {
  background: white;
  border-radius: 48rpx;
  padding: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  gap: 32rpx;
  transition: all 0.2s ease-out;
}

.primary-stat-item:hover {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-2rpx);
}

/* 次要统计 - 更紧凑的展示 */
.secondary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.secondary-stat-item {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  gap: 24rpx;
  transition: all 0.2s ease-out;
}

.secondary-stat-item:hover {
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-1rpx);
}

/* 统计图标样式 */
.stat-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon-wrapper.primary {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.stat-icon-wrapper.success {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.stat-icon-wrapper.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-icon-wrapper.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.secondary-stat-item .stat-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 16rpx;
}

.stat-icon {
  font-size: 32rpx;
}

.secondary-stat-item .stat-icon {
  font-size: 24rpx;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #171717;
  line-height: 1.25;
}

.secondary-stat-item .stat-number {
  font-size: 36rpx;
  font-weight: 600;
}

.stat-label {
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

.secondary-stat-item .stat-label {
  font-size: 24rpx;
}

/* ==================== 功能菜单区域 ==================== */
.menu-section {
  padding: 0 32rpx;
  margin-bottom: 64rpx;
}

.menu-container {
  background: white;
  border-radius: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx 48rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.2s ease-out;
  cursor: pointer;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item:active {
  background-color: #e5e5e5;
  transform: scale(0.98);
}

/* 添加微妙的点击反馈 */
.menu-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.15s ease-out;
}

.menu-item:active::after {
  opacity: 1;
}

.menu-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  flex-shrink: 0;
  transition: all 0.2s ease-out;
}

.menu-icon-wrapper.history {
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
}

.menu-icon-wrapper.settings {
  background: linear-gradient(135deg, #f5f5f5, #e5e5e5);
}

.menu-icon-wrapper.about {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.menu-icon-wrapper.help {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.menu-item:hover .menu-icon-wrapper {
  transform: scale(1.1);
}

.menu-icon {
  font-size: 36rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #171717;
  line-height: 1.25;
}

.menu-desc {
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-out;
}

.menu-item:hover .menu-arrow {
  background-color: #e0e7ff;
  transform: translateX(4rpx);
}

.arrow-icon {
  font-size: 32rpx;
  color: #a3a3a3;
  font-weight: 600;
}

.menu-item:hover .arrow-icon {
  color: #4f46e5;
}

/* ==================== 底部版本信息区域 ==================== */
.footer-section {
  padding: 96rpx 32rpx 64rpx;
}

.version-info {
  text-align: center;
  padding: 64rpx;
  background: white;
  border-radius: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f5f5f5;
}

.version-text {
  display: block;
  font-size: 28rpx;
  color: #525252;
  font-weight: 500;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #a3a3a3;
  line-height: 1.5;
}

/* ==================== 响应式适配 ==================== */
/* 小屏幕适配 */
@media (max-width: 375px) {
  .primary-stats,
  .secondary-stats {
    grid-template-columns: 1fr;
  }

  .primary-stat-item,
  .secondary-stat-item {
    padding: 32rpx;
  }

  .avatar {
    width: 112rpx;
    height: 112rpx;
  }

  .username {
    font-size: 36rpx;
  }
}

/* 大屏幕优化 */
@media (min-width: 768px) {
  .profile-container {
    max-width: 750rpx;
    margin: 0 auto;
  }

  .stats-container {
    flex-direction: row;
    gap: 48rpx;
  }

  .primary-stats,
  .secondary-stats {
    flex: 1;
    grid-template-columns: 1fr;
  }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-section,
.menu-section,
.footer-section {
  animation: fadeInUp 0.6s ease-out;
}

.stats-section {
  animation-delay: 0.1s;
}

.menu-section {
  animation-delay: 0.2s;
}

.footer-section {
  animation-delay: 0.3s;
}

/* 加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s ease-in-out infinite;
}
</style>
